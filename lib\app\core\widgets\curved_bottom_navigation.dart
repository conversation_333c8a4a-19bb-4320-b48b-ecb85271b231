import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CurvedBottomNavigation extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<CurvedBottomNavigationItem> items;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final double height;
  final bool showLabels; // minimal style: only active label by default
  final bool floating; // floating dock look
  final EdgeInsets? padding;

  const CurvedBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.height = 64,
    this.showLabels = true,
    this.floating = true,
    this.padding,
  });

  @override
  State<CurvedBottomNavigation> createState() => _CurvedBottomNavigationState();
}

class _CurvedBottomNavigationState extends State<CurvedBottomNavigation>
    with TickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 280));
    _animation =
        CurvedAnimation(parent: _controller, curve: Curves.easeInOutCubic);
  }

  @override
  void didUpdateWidget(covariant CurvedBottomNavigation oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentIndex != widget.currentIndex) {
      _controller.forward(from: 0);
      HapticFeedback.selectionClick();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final safeBottom = MediaQuery.of(context).padding.bottom;
    final bg =
        widget.backgroundColor ?? theme.colorScheme.surface.withOpacity(0.6);
    final selected = widget.selectedItemColor ?? theme.colorScheme.primary;
    final unselected = widget.unselectedItemColor ??
        theme.colorScheme.onSurface.withOpacity(0.6);

    final bar = _MinimalDock(
      height: widget.height,
      items: widget.items,
      currentIndex: widget.currentIndex,
      onTap: widget.onTap,
      background: bg,
      selected: selected,
      unselected: unselected,
      showLabels: widget.showLabels,
      animation: _animation,
    );

    final content = widget.floating
        ? Padding(
            padding: widget.padding ??
                EdgeInsets.fromLTRB(
                    16, 8, 16, (safeBottom > 0 ? safeBottom : 12)),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(18),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 16, sigmaY: 16),
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    color: bg,
                    border: Border.all(color: Colors.white.withOpacity(0.08)),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.08),
                        blurRadius: 18,
                        offset: const Offset(0, 8),
                      ),
                    ],
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: bar,
                ),
              ),
            ),
          )
        : Padding(
            padding: EdgeInsets.only(bottom: safeBottom),
            child: DecoratedBox(
              decoration: BoxDecoration(
                color: bg,
                border: Border(
                    top: BorderSide(
                        color: Colors.black.withOpacity(0.05), width: 0.6)),
              ),
              child: bar,
            ),
          );

    return SafeArea(
      top: false,
      child: SizedBox(
        height: (widget.floating
            ? widget.height + (safeBottom > 0 ? safeBottom : 12) + 16
            : widget.height + safeBottom),
        child: content,
      ),
    );
  }
}

class _MinimalDock extends StatelessWidget {
  final double height;
  final List<CurvedBottomNavigationItem> items;
  final int currentIndex;
  final void Function(int) onTap;
  final Color background;
  final Color selected;
  final Color unselected;
  final bool showLabels;
  final Animation<double> animation;

  const _MinimalDock({
    required this.height,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    required this.background,
    required this.selected,
    required this.unselected,
    required this.showLabels,
    required this.animation,
  });

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final itemWidth = width / items.length;

    return SizedBox(
      height: height,
      child: Stack(
        alignment: Alignment.centerLeft,
        children: [
          // Animated pill indicator behind active icon
          AnimatedBuilder(
            animation: animation,
            builder: (context, _) {
              final dx = (currentIndex + 0.5) * itemWidth - 24;
              return Transform.translate(
                offset: Offset(dx, 0),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Container(
                    width: 48,
                    height: 36,
                    decoration: BoxDecoration(
                      color: selected.withOpacity(0.12),
                      borderRadius: BorderRadius.circular(14),
                    ),
                  ),
                ),
              );
            },
          ),
          Row(
            children: List.generate(items.length, (i) {
              final selectedItem = i == currentIndex;
              return Expanded(
                child: InkWell(
                  onTap: () => onTap(i),
                  borderRadius: BorderRadius.circular(16),
                  child: SizedBox(
                    height: height,
                    child: Center(
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 220),
                        curve: Curves.easeOut,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              items[i].icon,
                              size: 22,
                              color: selectedItem ? selected : unselected,
                            ),
                            if (showLabels) ...[
                              AnimatedSize(
                                duration: const Duration(milliseconds: 220),
                                curve: Curves.easeOut,
                                child: SizedBox(
                                  width: selectedItem ? 8 : 0,
                                ),
                              ),
                              AnimatedDefaultTextStyle(
                                duration: const Duration(milliseconds: 220),
                                curve: Curves.easeOut,
                                style: TextStyle(
                                  color: selectedItem
                                      ? selected
                                      : Colors.transparent,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                ),
                                child: Text(selectedItem ? items[i].label : ''),
                              ),
                            ]
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}

class CurvedBottomNavigationItem {
  final IconData icon;
  final String label;

  const CurvedBottomNavigationItem({
    required this.icon,
    required this.label,
  });
}
