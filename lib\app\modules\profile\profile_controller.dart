import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/services/storage_service.dart';
import '../../routes/app_routes.dart';

class ProfileController extends GetxController {
  final RxString userName = '<PERSON>'.obs;
  final RxString userEmail = '<EMAIL>'.obs;
  final RxString userPhone = '****** 567 8900'.obs;
  final RxString userBio =
      'Flutter developer passionate about creating beautiful apps'.obs;
  final RxString joinDate = 'January 2024'.obs;
  final RxInt totalProjects = 12.obs;
  final RxInt completedTasks = 156.obs;
  final RxInt activeProjects = 3.obs;
  final RxString profileImageUrl = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _loadUserData();
  }

  void _loadUserData() {
    final userData = StorageService.to.userData;
    if (userData != null) {
      userName.value = userData['name'] ?? '<PERSON>';
      userEmail.value = userData['email'] ?? '<EMAIL>';
      userPhone.value = userData['phone'] ?? '****** 567 8900';
      userBio.value = userData['bio'] ??
          'Flutter developer passionate about creating beautiful apps';
      joinDate.value = userData['joinDate'] ?? 'January 2024';
      totalProjects.value = userData['totalProjects'] ?? 12;
      completedTasks.value = userData['completedTasks'] ?? 156;
      activeProjects.value = userData['activeProjects'] ?? 3;
      profileImageUrl.value = userData['profileImageUrl'] ?? '';
    }
  }

  void editProfile() {
    Get.dialog(
      AlertDialog(
        title: const Text('Edit Profile'),
        content: const Text('Profile editing feature coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void changePassword() {
    Get.dialog(
      AlertDialog(
        title: const Text('Change Password'),
        content: const Text('Password change feature coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void managePrivacy() {
    Get.dialog(
      AlertDialog(
        title: const Text('Privacy Settings'),
        content: const Text('Privacy management feature coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void viewActivity() {
    Get.dialog(
      AlertDialog(
        title: const Text('Activity Log'),
        content: const Text('Activity log feature coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void exportData() {
    Get.dialog(
      AlertDialog(
        title: const Text('Export Data'),
        content: const Text('Data export feature coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void deleteAccount() {
    Get.dialog(
      AlertDialog(
        title: const Text('Delete Account'),
        content: const Text(
            'Are you sure you want to delete your account? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.snackbar(
                'Account Deletion',
                'Account deletion feature coming soon!',
                snackPosition: SnackPosition.BOTTOM,
                duration: const Duration(seconds: 2),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> logout() async {
    Get.dialog(
      AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              await StorageService.to.clearUserData();
              Get.offAllNamed(AppRoutes.login);
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  void shareProfile() {
    Get.snackbar(
      'Share Profile',
      'Profile sharing feature coming soon!',
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 2),
    );
  }

  void changeProfilePicture() {
    Get.dialog(
      AlertDialog(
        title: const Text('Change Profile Picture'),
        content: const Text('Profile picture change feature coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
