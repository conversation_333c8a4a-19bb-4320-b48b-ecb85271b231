import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/services/storage_service.dart';
import '../../../core/services/network_service.dart';
import '../../../routes/app_routes.dart';
import '../../../data/models/auth_response.dart';

class LoginController extends GetxController {
  final formKey = GlobalKey<FormState>();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();

  final RxBool isLoading = false.obs;
  final RxBool obscurePassword = true.obs;
  final RxBool rememberMe = false.obs;

  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  void togglePasswordVisibility() {
    obscurePassword.value = !obscurePassword.value;
  }

  void toggleRememberMe() {
    rememberMe.value = !rememberMe.value;
  }

  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!GetUtils.isEmail(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  Future<void> login() async {
    if (!formKey.currentState!.validate()) return;

    try {
      isLoading.value = true;

      final response = await NetworkService.to.post('/auth/login', data: {
        'email': emailController.text.trim(),
        'password': passwordController.text,
        'remember_me': rememberMe.value,
      });

      if (response.statusCode == 200) {
        final authResponse = AuthResponse.fromJson(response.data);

        // Save tokens and user data
        StorageService.to.accessToken = authResponse.accessToken;
        StorageService.to.refreshToken = authResponse.refreshToken;
        StorageService.to.userData = authResponse.user.toJson();

        Get.snackbar(
          'Success',
          'Welcome back!',
          snackPosition: SnackPosition.TOP,
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );

        Get.offAllNamed(AppRoutes.mainNavigation);
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        'Login failed. Please check your credentials.',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withOpacity(0.8),
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loginWithGoogle() async {
    try {
      isLoading.value = true;

      // Simulate Google login
      await Future.delayed(const Duration(seconds: 2));

      Get.snackbar(
        'Info',
        'Google login will be implemented soon',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.blue.withOpacity(0.8),
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loginWithApple() async {
    try {
      isLoading.value = true;

      // Simulate Apple login
      await Future.delayed(const Duration(seconds: 2));

      Get.snackbar(
        'Info',
        'Apple login will be implemented soon',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.black.withOpacity(0.8),
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  void goToForgotPassword() {
    Get.toNamed(AppRoutes.forgotPassword);
  }

  Future<void> devLogin() async {
    try {
      isLoading.value = true;

      // Simulate dev login with dummy data
      await Future.delayed(const Duration(seconds: 1));

      // Create dummy user data for development
      final dummyUserData = {
        'id': 'dev_user_123',
        'name': 'Developer User',
        'email': '<EMAIL>',
        'phone': '****** 0123',
        'bio': 'Backend developer with access to admin features',
        'joinDate': 'December 2023',
        'totalProjects': 25,
        'completedTasks': 342,
        'activeProjects': 8,
        'profileImageUrl': '',
        'role': 'developer',
        'permissions': ['admin', 'backend_access', 'debug_mode'],
      };

      // Save dummy tokens and user data
      StorageService.to.accessToken =
          'dev_access_token_${DateTime.now().millisecondsSinceEpoch}';
      StorageService.to.refreshToken =
          'dev_refresh_token_${DateTime.now().millisecondsSinceEpoch}';
      StorageService.to.userData = dummyUserData;

      Get.snackbar(
        'Developer Login',
        'Logged in as Developer with backend access',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.purple.withValues(alpha: 0.8),
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      Get.offAllNamed(AppRoutes.mainNavigation);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Developer login failed',
        snackPosition: SnackPosition.TOP,
        backgroundColor: Colors.red.withValues(alpha: 0.8),
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
}
