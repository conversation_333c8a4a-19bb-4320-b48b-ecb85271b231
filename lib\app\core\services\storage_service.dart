import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import '../constants/app_constants.dart';

class StorageService extends GetxService {
  static StorageService get to => Get.find();

  late GetStorage _box;

  Future<StorageService> init() async {
    await GetStorage.init();
    _box = GetStorage();
    return this;
  }

  // Generic methods
  T? read<T>(String key) {
    return _box.read<T>(key);
  }

  Future<void> write(String key, dynamic value) async {
    await _box.write(key, value);
  }

  Future<void> remove(String key) async {
    await _box.remove(key);
  }

  Future<void> clear() async {
    await _box.erase();
  }

  // App specific methods
  bool get isFirstTime => read<bool>(AppConstants.keyIsFirstTime) ?? true;
  set isFirstTime(bool value) => write(AppConstants.keyIsFirstTime, value);

  String? get accessToken => read<String>(AppConstants.keyAccessToken);
  set accessToken(String? value) {
    if (value != null) {
      write(AppConstants.keyAccessToken, value);
    } else {
      remove(AppConstants.keyAccessToken);
    }
  }

  String? get refreshToken => read<String>(AppConstants.keyRefreshToken);
  set refreshToken(String? value) {
    if (value != null) {
      write(AppConstants.keyRefreshToken, value);
    } else {
      remove(AppConstants.keyRefreshToken);
    }
  }

  Map<String, dynamic>? get userData =>
      read<Map<String, dynamic>>(AppConstants.keyUserData);
  set userData(Map<String, dynamic>? value) {
    if (value != null) {
      write(AppConstants.keyUserData, value);
    } else {
      remove(AppConstants.keyUserData);
    }
  }

  String? get themeMode => read<String>(AppConstants.keyThemeMode);
  set themeMode(String? value) {
    if (value != null) {
      write(AppConstants.keyThemeMode, value);
    } else {
      remove(AppConstants.keyThemeMode);
    }
  }

  bool get isLoggedIn => accessToken != null;

  bool get isDarkMode => read<bool>('dark_mode') ?? false;

  // Helper methods for settings
  bool getBool(String key) => read<bool>(key) ?? false;
  String getString(String key) => read<String>(key) ?? '';
  double getDouble(String key) => read<double>(key) ?? 0.0;
  int getInt(String key) => read<int>(key) ?? 0;

  Future<void> setBool(String key, bool value) async {
    await write(key, value);
  }

  Future<void> setString(String key, String value) async {
    await write(key, value);
  }

  Future<void> setDouble(String key, double value) async {
    await write(key, value);
  }

  Future<void> setInt(String key, int value) async {
    await write(key, value);
  }

  Future<void> clearUserData() async {
    await remove(AppConstants.keyAccessToken);
    await remove(AppConstants.keyRefreshToken);
    await remove(AppConstants.keyUserData);
  }
}
