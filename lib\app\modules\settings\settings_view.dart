import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/widgets/flat_list_tile.dart';
import '../../core/widgets/flat_button.dart';
import 'settings_controller.dart';

class SettingsView extends GetView<SettingsController> {
  const SettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Settings',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Appearance Section
            _buildSectionHeader(context, 'Appearance'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      controller.isDarkMode.value
                          ? Icons.dark_mode
                          : Icons.light_mode,
                      color: theme.colorScheme.primary,
                      size: 20,
                    ),
                  ),
                  title: 'Dark Mode',
                  subtitle:
                      controller.isDarkMode.value ? 'Enabled' : 'Disabled',
                  trailing: Switch(
                    value: controller.isDarkMode.value,
                    onChanged: (_) => controller.toggleDarkMode(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleDarkMode,
                )),

            const SizedBox(height: 8),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: theme.colorScheme.secondary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.text_fields,
                  color: theme.colorScheme.secondary,
                  size: 20,
                ),
              ),
              title: 'Font Size',
              subtitle: 'Adjust text size',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withOpacity(0.5),
              ),
              onTap: () => _showFontSizeDialog(context),
            ),

            const SizedBox(height: 24),

            // Notifications Section
            _buildSectionHeader(context, 'Notifications'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.notifications,
                      color: Colors.orange,
                      size: 20,
                    ),
                  ),
                  title: 'Push Notifications',
                  subtitle: controller.notificationsEnabled.value
                      ? 'Enabled'
                      : 'Disabled',
                  trailing: Switch(
                    value: controller.notificationsEnabled.value,
                    onChanged: (_) => controller.toggleNotifications(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleNotifications,
                )),

            const SizedBox(height: 24),

            // Security Section
            _buildSectionHeader(context, 'Security'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.fingerprint,
                      color: Colors.green,
                      size: 20,
                    ),
                  ),
                  title: 'Biometric Authentication',
                  subtitle: controller.biometricEnabled.value
                      ? 'Enabled'
                      : 'Disabled',
                  trailing: Switch(
                    value: controller.biometricEnabled.value,
                    onChanged: (_) => controller.toggleBiometric(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleBiometric,
                )),

            const SizedBox(height: 24),

            // Data & Storage Section
            _buildSectionHeader(context, 'Data & Storage'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.backup,
                      color: Colors.blue,
                      size: 20,
                    ),
                  ),
                  title: 'Auto Backup',
                  subtitle:
                      controller.autoBackup.value ? 'Enabled' : 'Disabled',
                  trailing: Switch(
                    value: controller.autoBackup.value,
                    onChanged: (_) => controller.toggleAutoBackup(),
                    activeColor: theme.colorScheme.primary,
                  ),
                  onTap: controller.toggleAutoBackup,
                )),

            const SizedBox(height: 8),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.clear_all,
                  color: Colors.red,
                  size: 20,
                ),
              ),
              title: 'Clear Cache',
              subtitle: 'Free up storage space',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withOpacity(0.5),
              ),
              onTap: controller.clearCache,
            ),

            const SizedBox(height: 24),

            // General Section
            _buildSectionHeader(context, 'General'),
            const SizedBox(height: 12),
            Obx(() => FlatListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.purple.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.language,
                      color: Colors.purple,
                      size: 20,
                    ),
                  ),
                  title: 'Language',
                  subtitle: controller.selectedLanguage.value,
                  trailing: Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: theme.colorScheme.onSurface.withOpacity(0.5),
                  ),
                  onTap: () => _showLanguageDialog(context),
                )),

            const SizedBox(height: 8),
            FlatListTile(
              leading: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.teal.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.info,
                  color: Colors.teal,
                  size: 20,
                ),
              ),
              title: 'About',
              subtitle: 'App version and info',
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withOpacity(0.5),
              ),
              onTap: controller.showAbout,
            ),

            const SizedBox(height: 32),

            // Reset Button
            FlatButton(
              text: 'Reset All Settings',
              onPressed: controller.resetSettings,
              backgroundColor: Colors.red.withOpacity(0.1),
              textColor: Colors.red,
              outlined: true,
              borderColor: Colors.red.withOpacity(0.3),
              width: double.infinity,
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.primary,
          ),
    );
  }

  void _showLanguageDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: const Text('Select Language'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: controller.availableLanguages.length,
            itemBuilder: (context, index) {
              final language = controller.availableLanguages[index];
              return Obx(() => RadioListTile<String>(
                    title: Text(language),
                    value: language,
                    groupValue: controller.selectedLanguage.value,
                    onChanged: (value) {
                      if (value != null) {
                        controller.changeLanguage(value);
                        Get.back();
                      }
                    },
                  ));
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showFontSizeDialog(BuildContext context) {
    Get.dialog(
      AlertDialog(
        title: const Text('Font Size'),
        content: Obx(() => Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Sample Text',
                  style: TextStyle(fontSize: 16 * controller.fontSize.value),
                ),
                const SizedBox(height: 16),
                Slider(
                  value: controller.fontSize.value,
                  min: 0.8,
                  max: 1.4,
                  divisions: 6,
                  label: '${(controller.fontSize.value * 100).round()}%',
                  onChanged: controller.changeFontSize,
                ),
              ],
            )),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }
}
