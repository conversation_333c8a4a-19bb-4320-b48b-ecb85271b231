import 'package:flutter/material.dart';
import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:google_fonts/google_fonts.dart';

// Custom theme extension for additional colors and gradients
@immutable
class AppColorsExtension extends ThemeExtension<AppColorsExtension> {
  const AppColorsExtension({
    required this.primaryGradient,
    required this.secondaryGradient,
    required this.backgroundGradient,
    this.glassWhite,
    this.glassBorder,
    this.textOnGlass,
    this.shadowLight,
    this.shadowMedium,
    this.shadowDark,
    required this.success,
    required this.error,
    required this.warning,
    required this.info,
  });

  final List<Color> primaryGradient;
  final List<Color> secondaryGradient;
  final List<Color> backgroundGradient;
  // Add optional glass/shadow palette used by components
  final Color? glassWhite;
  final Color? glassBorder;
  final Color? textOnGlass;
  final Color? shadowLight;
  final Color? shadowMedium;
  final Color? shadowDark;
  final Color success;
  final Color error;
  final Color warning;
  final Color info;

  @override
  AppColorsExtension copyWith({
    List<Color>? primaryGradient,
    List<Color>? secondaryGradient,
    List<Color>? backgroundGradient,
    Color? glassWhite,
    Color? glassBorder,
    Color? textOnGlass,
    Color? shadowLight,
    Color? shadowMedium,
    Color? shadowDark,
    Color? success,
    Color? error,
    Color? warning,
    Color? info,
  }) {
    return AppColorsExtension(
      primaryGradient: primaryGradient ?? this.primaryGradient,
      secondaryGradient: secondaryGradient ?? this.secondaryGradient,
      backgroundGradient: backgroundGradient ?? this.backgroundGradient,
      glassWhite: glassWhite ?? this.glassWhite,
      glassBorder: glassBorder ?? this.glassBorder,
      textOnGlass: textOnGlass ?? this.textOnGlass,
      shadowLight: shadowLight ?? this.shadowLight,
      shadowMedium: shadowMedium ?? this.shadowMedium,
      shadowDark: shadowDark ?? this.shadowDark,
      success: success ?? this.success,
      error: error ?? this.error,
      warning: warning ?? this.warning,
      info: info ?? this.info,
    );
  }

  @override
  AppColorsExtension lerp(ThemeExtension<AppColorsExtension>? other, double t) {
    if (other is! AppColorsExtension) {
      return this;
    }
    return AppColorsExtension(
      primaryGradient: <Color>[
        Color.lerp(primaryGradient[0], other.primaryGradient[0], t)!,
        Color.lerp(primaryGradient[1], other.primaryGradient[1], t)!,
      ],
      secondaryGradient: <Color>[
        Color.lerp(secondaryGradient[0], other.secondaryGradient[0], t)!,
        Color.lerp(secondaryGradient[1], other.secondaryGradient[1], t)!,
      ],
      backgroundGradient: <Color>[
        Color.lerp(backgroundGradient[0], other.backgroundGradient[0], t)!,
        Color.lerp(backgroundGradient[1], other.backgroundGradient[1], t)!,
      ],
      glassWhite: Color.lerp(glassWhite, other.glassWhite, t),
      glassBorder: Color.lerp(glassBorder, other.glassBorder, t),
      textOnGlass: Color.lerp(textOnGlass, other.textOnGlass, t),
      shadowLight: Color.lerp(shadowLight, other.shadowLight, t),
      shadowMedium: Color.lerp(shadowMedium, other.shadowMedium, t),
      shadowDark: Color.lerp(shadowDark, other.shadowDark, t),
      success: Color.lerp(success, other.success, t)!,
      error: Color.lerp(error, other.error, t)!,
      warning: Color.lerp(warning, other.warning, t)!,
      info: Color.lerp(info, other.info, t)!,
    );
  }

  // Light theme colors
  static const AppColorsExtension light = AppColorsExtension(
    primaryGradient: <Color>[Color(0xFFB8A9FF), Color(0xFFFFA9E7)],
    secondaryGradient: <Color>[Color(0xFFA9FFFF), Color(0xFFB8A9FF)],
    backgroundGradient: <Color>[Color(0xFFF5F3FF), Color(0xFFE8F4FD)],
    glassWhite: Color(0x1AFFFFFF),
    glassBorder: Color(0x33FFFFFF),
    textOnGlass: Color(0xFF4A5568),
    shadowLight: Color(0x1A000000),
    shadowMedium: Color(0x33000000),
    shadowDark: Color(0x4D000000),
    success: Color(0xFF68D391),
    error: Color(0xFFF56565),
    warning: Color(0xFFED8936),
    info: Color(0xFF63B3ED),
  );

  // Dark theme colors
  static const AppColorsExtension dark = AppColorsExtension(
    primaryGradient: <Color>[Color(0xFFB8A9FF), Color(0xFFFFA9E7)],
    secondaryGradient: <Color>[Color(0xFFA9FFFF), Color(0xFFB8A9FF)],
    backgroundGradient: <Color>[Color(0xFF1A1A2E), Color(0xFF16213E)],
    glassWhite: Color(0x0AFFFFFF),
    glassBorder: Color(0x1AFFFFFF),
    textOnGlass: Color(0xFFE2E8F0),
    shadowLight: Color(0x33000000),
    shadowMedium: Color(0x4D000000),
    shadowDark: Color(0x66000000),
    success: Color(0xFF68D391),
    error: Color(0xFFF56565),
    warning: Color(0xFFED8936),
    info: Color(0xFF63B3ED),
  );
}

class AppTheme {
  // Define our custom colors directly in the theme
  static const Color _primaryPastel = Color(0xFFB8A9FF);
  static const Color _secondaryPastel = Color(0xFFFFA9E7);
  static const Color _accentPastel = Color(0xFFA9FFFF);
  static const Color _backgroundPastel = Color(0xFFF5F3FF);

  static ThemeData get lightTheme {
    return FlexThemeData.light(
      scheme: FlexScheme.custom,
      primary: _primaryPastel,
      secondary: _secondaryPastel,
      tertiary: _accentPastel,
      surface: _backgroundPastel,
      scaffoldBackground: _backgroundPastel,
      appBarStyle: FlexAppBarStyle.background,
      subThemesData: const FlexSubThemesData(
        blendOnLevel: 10,
        blendOnColors: false,
        useMaterial3Typography: true,
        useM2StyleDividerInM3: true,
        // Flat design - reduced elevations
        elevatedButtonElevation: 0,
        cardElevation: 0,
        dialogElevation: 0,
        bottomNavigationBarElevation: 0,
        navigationBarElevation: 0,
        elevatedButtonSchemeColor: SchemeColor.onPrimaryContainer,
        elevatedButtonSecondarySchemeColor: SchemeColor.primaryContainer,
        outlinedButtonOutlineSchemeColor: SchemeColor.primary,
        toggleButtonsBorderSchemeColor: SchemeColor.primary,
        segmentedButtonSchemeColor: SchemeColor.primary,
        segmentedButtonBorderSchemeColor: SchemeColor.primary,
        unselectedToggleIsColored: true,
        sliderBaseSchemeColor: SchemeColor.primary,
        sliderIndicatorSchemeColor: SchemeColor.onPrimary,
        sliderValueTinted: true,
        inputDecoratorSchemeColor: SchemeColor.primary,
        inputDecoratorIsFilled: true,
        inputDecoratorBorderType: FlexInputBorderType.outline,
        inputDecoratorRadius: 12.0,
        inputDecoratorUnfocusedHasBorder: true,
        inputDecoratorFocusedBorderWidth: 1.5,
        fabUseShape: true,
        fabAlwaysCircular: true,
        fabSchemeColor: SchemeColor.tertiary,
        chipSchemeColor: SchemeColor.primaryContainer,
        chipSelectedSchemeColor: SchemeColor.primary,
        chipRadius: 12.0,
        cardRadius: 12.0,
        popupMenuRadius: 12.0,
        popupMenuElevation: 0.0,
        dialogRadius: 16.0,
        timePickerDialogRadius: 16.0,
        snackBarRadius: 12.0,
        snackBarElevation: 0.0,
        appBarScrolledUnderElevation: 0.0,
        bottomSheetRadius: 16.0,
        bottomSheetElevation: 0.0,
        bottomNavigationBarMutedUnselectedLabel: true,
        bottomNavigationBarMutedUnselectedIcon: true,
        menuRadius: 12.0,
        menuElevation: 0.0,
        menuBarRadius: 12.0,
        menuBarElevation: 0.0,
        navigationBarSelectedLabelSchemeColor: SchemeColor.onSurface,
        navigationBarMutedUnselectedLabel: true,
        navigationBarMutedUnselectedIcon: true,
        navigationBarIndicatorSchemeColor: SchemeColor.secondaryContainer,
        navigationBarIndicatorOpacity: 0.12,
        navigationBarIndicatorRadius: 12.0,
        navigationRailSelectedLabelSchemeColor: SchemeColor.onSurface,
        navigationRailMutedUnselectedLabel: true,
        navigationRailMutedUnselectedIcon: true,
        navigationRailIndicatorSchemeColor: SchemeColor.secondaryContainer,
        navigationRailIndicatorOpacity: 0.12,
        navigationRailIndicatorRadius: 12.0,
      ),
      keyColors: const FlexKeyColors(
        useSecondary: true,
        useTertiary: true,
        keepPrimary: true,
        keepSecondary: true,
        keepTertiary: true,
      ),
      visualDensity: FlexColorScheme.comfortablePlatformDensity,
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
      fontFamily: GoogleFonts.inter().fontFamily,
      extensions: const <ThemeExtension<dynamic>>[
        AppColorsExtension.light,
      ],
    );
  }

  static ThemeData get darkTheme {
    return FlexThemeData.dark(
      scheme: FlexScheme.custom,
      primary: _primaryPastel,
      secondary: _secondaryPastel,
      tertiary: _accentPastel,
      surface: const Color(0xFF1A1A2E),
      scaffoldBackground: const Color(0xFF0F172A),
      appBarStyle: FlexAppBarStyle.background,
      subThemesData: const FlexSubThemesData(
        blendOnLevel: 20,
        blendOnColors: false,
        useMaterial3Typography: true,
        useM2StyleDividerInM3: true,
        // Flat design - reduced elevations
        elevatedButtonElevation: 0,
        cardElevation: 0,
        dialogElevation: 0,
        bottomNavigationBarElevation: 0,
        navigationBarElevation: 0,
        elevatedButtonSchemeColor: SchemeColor.onPrimaryContainer,
        elevatedButtonSecondarySchemeColor: SchemeColor.primaryContainer,
        outlinedButtonOutlineSchemeColor: SchemeColor.primary,
        toggleButtonsBorderSchemeColor: SchemeColor.primary,
        segmentedButtonSchemeColor: SchemeColor.primary,
        segmentedButtonBorderSchemeColor: SchemeColor.primary,
        unselectedToggleIsColored: true,
        sliderBaseSchemeColor: SchemeColor.primary,
        sliderIndicatorSchemeColor: SchemeColor.onPrimary,
        sliderValueTinted: true,
        inputDecoratorSchemeColor: SchemeColor.primary,
        inputDecoratorIsFilled: true,
        inputDecoratorBorderType: FlexInputBorderType.outline,
        inputDecoratorRadius: 12.0,
        inputDecoratorUnfocusedHasBorder: true,
        inputDecoratorFocusedBorderWidth: 1.5,
        fabUseShape: true,
        fabAlwaysCircular: true,
        fabSchemeColor: SchemeColor.tertiary,
        chipSchemeColor: SchemeColor.primaryContainer,
        chipSelectedSchemeColor: SchemeColor.primary,
        chipRadius: 12.0,
        cardRadius: 12.0,
        popupMenuRadius: 12.0,
        popupMenuElevation: 0.0,
        dialogRadius: 16.0,
        timePickerDialogRadius: 16.0,
        snackBarRadius: 12.0,
        snackBarElevation: 0.0,
        appBarScrolledUnderElevation: 0.0,
        bottomSheetRadius: 16.0,
        bottomSheetElevation: 0.0,
        bottomNavigationBarMutedUnselectedLabel: true,
        bottomNavigationBarMutedUnselectedIcon: true,
        menuRadius: 12.0,
        menuElevation: 0.0,
        menuBarRadius: 12.0,
        menuBarElevation: 0.0,
        navigationBarSelectedLabelSchemeColor: SchemeColor.onSurface,
        navigationBarMutedUnselectedLabel: true,
        navigationBarMutedUnselectedIcon: true,
        navigationBarIndicatorSchemeColor: SchemeColor.secondaryContainer,
        navigationBarIndicatorOpacity: 0.12,
        navigationBarIndicatorRadius: 12.0,
        navigationRailSelectedLabelSchemeColor: SchemeColor.onSurface,
        navigationRailMutedUnselectedLabel: true,
        navigationRailMutedUnselectedIcon: true,
        navigationRailIndicatorSchemeColor: SchemeColor.secondaryContainer,
        navigationRailIndicatorOpacity: 0.12,
        navigationRailIndicatorRadius: 12.0,
      ),
      keyColors: const FlexKeyColors(
        useSecondary: true,
        useTertiary: true,
        keepPrimary: true,
        keepSecondary: true,
        keepTertiary: true,
      ),
      visualDensity: FlexColorScheme.comfortablePlatformDensity,
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
      fontFamily: GoogleFonts.inter().fontFamily,
      extensions: const <ThemeExtension<dynamic>>[
        AppColorsExtension.dark,
      ],
    );
  }
}

// Extension to easily access custom colors from theme
extension ThemeDataExtensions on ThemeData {
  AppColorsExtension get appColors => extension<AppColorsExtension>()!;
}
